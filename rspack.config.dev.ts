import path from 'path';
import fs from 'fs';
import {defineConfig} from '@rspack/cli';
import {rspack} from '@rspack/core';
require('./prepareEnv');
import {RsdoctorRspackPlugin} from '@rsdoctor/rspack-plugin';
// import RspackCircularDependencyPlugin from 'rspack-circular-dependency-plugin';
import TimingPlugin from './src/plugins/timingPlugin';
import {createHtmlPluginInstances, generateEntriesAndHtmlPlugins} from './src/utils/rspack';
import sharedDeps from './src/config/sharedDeps';
import {proxyConfig, DEV_PORT, HAIRUO_PATH} from './debug/proxyConfig';
import {httpProxyConfig, baseMiddleware, parseBody, serve} from './debug/middleware/server';
import {proxyPathRewriteForRsapck, remotesForDev} from './debug/remote';


const appDirectory = fs.realpathSync(process.cwd());
const resolveApp = (relativePath: string) => path.resolve(appDirectory, relativePath);

const STAGE = process.env.STAGE;

// Target browsers, see: https://github.com/browserslist/browserslist
const targets = ['chrome >= 87', 'edge >= 88', 'firefox >= 78', 'safari >= 14'];
// let errPaths: any[] = [];
export default (async () => {
    const entries = await generateEntriesAndHtmlPlugins({cwd: appDirectory, srcDirectory: 'src/entries'});
    const htmlPlugins = createHtmlPluginInstances({entries});

    // 将 process.env 中的变量收集起来，用于 DefinePlugin
    const envVariables = Object.keys(process.env).reduce<Record<string, string>>((acc, key) => {
        acc[`process.env.${key}`] = JSON.stringify(process.env[key]);
        return acc;
    }, {});

    return defineConfig({
        mode: 'development',
        devtool: 'source-map',
        output: {
            publicPath: '/',
        },
        entry: entries.reduce((webpackEntry, appEntry) => {
            webpackEntry[appEntry.name] = appEntry.file;
            return webpackEntry;
        }, {}),
        resolve: {
            extensions: ['...', '.ts', '.tsx', '.jsx', '.js', '.json', '.mjs'],
            alias: {
                '@': resolveApp('src'),
            },
        },
        module: {
            rules: [
                {
                    test: [/\.bmp$/, /\.gif$/, /\.jpe?g$/, /\.png$/],
                    type: 'asset',
                },
                {
                    test: /\.(jsx?|tsx?)$/,
                    resolve: {fullySpecified: false},
                    use: [
                        {
                            loader: 'builtin:swc-loader',
                            options: {
                                jsc: {
                                    parser: {
                                        syntax: 'typescript',
                                        tsx: true,
                                    },
                                    transform: {
                                        react: {
                                            runtime: 'automatic',
                                            development: true,
                                            refresh: false,
                                        },
                                    },
                                },
                                env: {targets},
                            },
                        },
                    ],
                },
                {
                    test: [/\.less$/],
                    use: [
                        {
                            loader: 'less-loader',
                            options: {lessOptions: {math: 'always'}},
                        },
                    ],
                    type: 'css/auto',
                },
                {
                    test: /\.svg$/,
                    resourceQuery: {
                        not: /^\?raw|url$/,
                    },
                    oneOf: [
                        {
                            // 如果挂了`?react`的，就直接转成组件返回
                            resourceQuery: /^\?react$/,
                            use: ['@svgr/webpack'],
                        },
                        {
                            resourceQuery: {
                                not: /^\?react$/,
                            },
                            type: 'asset',
                        },
                    ],
                },
            ],
            parser: {
                'css/auto': {
                    namedExports: false,
                },
            },
        },
        plugins: [
            new TimingPlugin(),
            new rspack.DefinePlugin({
                'process.env.STAGE': JSON.stringify(STAGE),
                ...envVariables,
            }),
            new rspack.container.ModuleFederationPluginV1({
                name: 'fecangjieapp',
                filename: 'cangjieApp/remoteEntry.js',
                remotes: remotesForDev,
                shared: sharedDeps,
            }),
            process.env.RSDOCTOR && new RsdoctorRspackPlugin({}),
            ...htmlPlugins, // Add all HtmlWebpackPlugin instances
            // new RspackCircularDependencyPlugin({
            //     onStart() {
            //         errPaths = [];
            //     },
            //     // 排除检测符合正则的文件
            //     exclude: /node_modules/,
            //     // 向 webpack 输出错误而不是警告
            //     failOnError: false,
            //     // 允许包含异步导入的循环
            //     // 举例：via import(/* webpackMode: "weak" */ './file.js')
            //     allowAsyncCycles: false,
            //     onDetected({paths}: any) {
            //         errPaths.push(paths);
            //     },
            //     onEnd() {
            //         // 输入到文件中
            //         const sortedByJson = errPaths.sort((a, b) =>
            //             JSON.stringify(a).localeCompare(JSON.stringify(b))
            //         );
            //         fs.writeFileSync(
            //             './circular.json',
            //             JSON.stringify(sortedByJson, null, 2),
            //             'utf8'
            //         );
            //     },
            // }),
        ],
        cache: true,
        experiments: {
            css: true,
            cache: {
                type: process.env.CACHE_TYPE as 'memory' | 'persistent' || 'persistent',
                buildDependencies: [__filename],
                version: '1.0.0', // 重大更改时可更新此版本号使原有缓存失效
            },
        },
        devServer: {
            port: DEV_PORT,
            host: '0.0.0.0',
            hot: true,
            historyApiFallback: {
                rewrites: [
                    {from: /^\/ad/, to: '/index.html'},
                    {from: /^\/mobile/, to: '/mobile.html'},
                ],
            },
            ...(
                proxyConfig ? {
                    proxy: [
                        {
                            context: [HAIRUO_PATH],
                            target: proxyConfig?.target,
                            secure: proxyConfig?.https,
                            changeOrigin: true,
                            onProxyRes: httpProxyConfig.onProxyRes,
                            selfHandleResponse: httpProxyConfig.selfHandleResponse,
                        },
                        {
                            context: ['/ash-api/'],
                            target: 'http://localhost:8848',
                            secure: false,
                            changeOrigin: true,
                        },
                        ...proxyPathRewriteForRsapck,
                    ],
                } : {
                    proxy: [
                        ...proxyPathRewriteForRsapck,
                    ],
                }
            ),
            allowedHosts: 'all',
            client: {
                overlay: false,
            },
            open: `http://${proxyConfig?.devHost || 'dev.qingge.baidu.com'}:${DEV_PORT}`,
            setupMiddlewares: (middlewares, devServer) => {
                if (!devServer) {
                    throw new Error('webpack-dev-server is not defined');
                }

                const middleware = proxyConfig ? baseMiddleware : parseBody;
                devServer.app?.use(middleware);
                if (!proxyConfig) {
                    middlewares.push(serve);
                }
                return middlewares;
            },
        },
    });
})();
