import {useEffect, useCallback, useMemo} from 'react';
import {useRequest} from 'huse';
import {useAsyncTaskBanner} from 'commonLibs/hooks/asyncTask';
import {useKeyOrientedArray} from '@/hooks/collection/array';
import {RowSelection, RowSelectionMethodsProps, useRowSelection} from '@/hooks/selection';
import {
    fetchFeedProjectList,
    updateFeedProject,
    updateFeedFastProjectFeedName,
    batchUpdateFeedProject,
    batchDeleteFeedProject,
    batchModFeedProject,
    batchSeperateUpdateRefreshProjects,
} from '@/api/manage/project';
import {FeedIdType} from '@/dicts/idType';
import {useQueryTableSort, OneUiSortParams} from '@/hooks/tableList/sorter';
import {Pagination, useQueryTablePagination} from '@/hooks/pagination';
import {Sorter} from '@/interface/report';
import {createError} from '@/utils/error';
import {MaterialList} from '@/interface/tableList';
import {ProjectItemOfFEED} from '@/interface/project';
import {makeLogger, getSaveEventByParams} from '@/modules/Ad/ManageCenter/common/utils';
import {PRODUCT} from '@/dicts/campaign';
import {useFeedIdTypeByQuery} from '@/hooks/tableList/query';
import {levelTypeMapForLogOrSwr} from '@/hooks/request/levelType';
import {DateRange} from '../../../context';

const FIELD = levelTypeMapForLogOrSwr.manageProjectList;
const sendLog = makeLogger({level: FIELD, 'extra_params': PRODUCT.FEED});
const initialListData = {rows: [], summary: {}, totalRowCount: 0};
export const getFcProjectRowKey = (record: any) => record.projectId;

interface IProps {
    reportType: number;
    filters: MaterialList.FilterItem[];
    date: DateRange;
    queryFields: {
        customFields: string[];
    };
}

export const getFeedProjectRowKey = (record: any) => record.projectFeedId;
type UseFeedProjectsListReturn = [
    {
        pending: boolean;
        error: Error | undefined;
        sorter: Sorter;
        data: {summary: Record<string, any>, rows: ProjectItemOfFEED[], totalCount: number};
        pagination: Pagination;
        selection: RowSelection;
        asyncTasks: {
            unreadTask: Array<{[key: string]: unknown}>;
            processingTask: Array<{[key: string]: unknown}>;
        };
    },
    {
        setPageNo: (pageNo: number) => void;
        setPageSize: (pageSize: number) => void;
        refresh: () => void;
        getProjectById: (key: string) => ProjectItemOfFEED | undefined;
        getProjectsByKeys: (keys: Array<string | number>) => Array<(ProjectItemOfFEED | undefined)>;
        onSort: (sorter: OneUiSortParams) => void;
        inlineSaveProject: (projectId: string, values: any) => Promise<ProjectItemOfFEED>;
        updateFastProjectFeedName: (projectId: string, values: any) => Promise<ProjectItemOfFEED>;
        batchUpdateProject: (values: any) => Promise<ProjectItemOfFEED>;
        batchModProject: (values: any) => Promise<ProjectItemOfFEED>;
        batchDeleteProject: () => Promise<void>;
        selectionOperations: RowSelectionMethodsProps;
    }
];

export function formatError(error: any, getProjectById: (id: number) => any) {
    const err = createError(error);
    err.materialName = '项目';
    err.getErrorInfos = (errors: any[]) => errors.map((error: any) => ({
        ...error,
        position: getProjectById(error.id)?.projectFeedName || error.id,
    }));
    return err;
}

export function useFeedProjectsList({filters, date, queryFields}: IProps): UseFeedProjectsListReturn {
    const {customFields: fields} = queryFields;
    const [sorter, onSort] = useQueryTableSort();
    const [pagination, {setPageNo, setPageSize}] = useQueryTablePagination();
    const {
        data: {rows: rawRows, totalRowCount: totalCount, summary} = initialListData,
        pending,
        error,
        refresh,
    } = useRequest(
        fetchFeedProjectList,
        {sorter, filters, date, fields, pagination},
    );

    const [rows, {
        getItemByKey: getProjectById,
        set: setProjects,
    }] = useKeyOrientedArray(rawRows, {getKey: getFeedProjectRowKey});

    useEffect(() => {
        setProjects(rawRows);
    }, [rawRows]);

    const getProjectsByKeys = useCallback(
        (keys: string[]) => keys.map(key => rows.find(item => getFeedProjectRowKey(item) === key)),
        [rows]
    );

    const listIds = useMemo(() => rows.map(getFeedProjectRowKey), [rows]);
    const [selection, selectionOperations] = useRowSelection({ids: listIds, totalCount});

    const [asyncTasks, asyncTaskMethods] = useAsyncTaskBanner({levelIdType: FeedIdType.PROJECT_LEVEL});
    const {startQueryAsyncTask} = asyncTaskMethods;

    useEffect(
        () => {
            selectionOperations.resetRowSelection();
        },
        [filters, date]
    );

    const updateFastProjectFeedName = useCallback(
        async (projectId: string, values: any) => {
            let data = null;
            try {
                const event = getSaveEventByParams(values, {aixProduct: PRODUCT.FEED});
                [data] = await updateFeedFastProjectFeedName(projectId, values);
                sendLog({event, source: 'inline'});
            }
            catch (error) {
                throw createError(error);
            }
            refresh();
            return data;
        },
        [refresh]
    );
    const inlineSaveProject = useCallback(
        async (projectId: string, values: any) => {
            let data = null;
            try {
                const event = getSaveEventByParams(values, {aixProduct: PRODUCT.FEED});
                [data] = await updateFeedProject(projectId, {...values, mandatoryOperation: 1});
                sendLog({event, source: 'inline'});
            }
            catch (error) {
                throw createError(error);
            }
            refresh();
            return data;
        },
        [refresh]
    );

    const getMaterialById = useCallback(
        (id: number) => {
            return rows.find((item: any) => [item.projectFeedId, item.projectId].includes(id));
        },
        [rows]
    );

    const decorateError = useCallback(error => {
        return formatError(error, getMaterialById);
    }, [getMaterialById]);

    const modBatchProjectsSeperate = useCallback(
        async (values: any) => {
            let data = null;
            const event = getSaveEventByParams(values, {aixProduct: PRODUCT.FEED, isForm: true});
            const {selectedCount: count} = selectionOperations.getSelectedInfo();
            const baseParams = {event, count, source: 'operation_bar'};
            try {
                data = await batchSeperateUpdateRefreshProjects(values);
                sendLog(baseParams);
            }
            catch (error) {
                const err = decorateError(error);
                if (err._normalized?.type === 'partial') {
                    sendLog(baseParams);
                    refresh();
                }
                throw err;
            }
            if (data.isAsyncTask) {
                startQueryAsyncTask(true);
                return;
            }
            refresh();
            return data.dataList;
        },
        [refresh, selectionOperations.getSelectedInfo, decorateError]
    );

    const batchModProject = useCallback(
        async (values: any) => {
            let data = null;
            const event = getSaveEventByParams(values, {aixProduct: PRODUCT.FEED, isForm: true});
            const selectionInfo = selectionOperations.getSelectedInfo();
            const baseParams = {event, count: selectionInfo.selectedCount, source: 'operation_bar'};
            try {
                data = await batchModFeedProject({selectionInfo, filters, timeRange: date}, values);
                sendLog(baseParams);
            }
            catch (error) {
                const err = formatError(error, getProjectById);
                if (err._normalized?.type === 'partial') {
                    sendLog(baseParams);
                    refresh();
                }
                throw err;
            }
            if (data.isAsyncTask) {
                startQueryAsyncTask(true);
                return;
            }
            refresh();
            return data;
        },
        [refresh, getProjectById, startQueryAsyncTask, selectionOperations.getSelectedInfo, filters, date]
    );

    const batchUpdateProject = useCallback(
        async (values: any) => {
            let data = null;
            const event = getSaveEventByParams(values, {aixProduct: PRODUCT.FEED, isForm: true});
            const selectionInfo = selectionOperations.getSelectedInfo();
            const baseParams = {event, count: selectionInfo.selectedCount, source: 'operation_bar'};
            try {
                [data] = await batchUpdateFeedProject(values);
                sendLog(baseParams);
            }
            catch (error) {
                const err = formatError(error, getProjectById);
                if (err._normalized?.type === 'partial') {
                    sendLog(baseParams);
                    refresh();
                }
                throw err;
            }
            refresh();
            return data;
        },
        [refresh, getProjectById, selectionOperations.getSelectedInfo]
    );

    const batchDeleteProject = useCallback(
        async () => {
            let data = null;
            try {
                const selectionInfo = selectionOperations.getSelectedInfo();
                const {selectedCount: count} = selectionInfo;
                const baseParams = {count, source: 'operation_bar'};
                sendLog({...baseParams, event: 'launch_delete'});
                data = await batchDeleteFeedProject({selectionInfo, filters, timeRange: date});
                sendLog({...baseParams, event: 'delete'});
            }
            catch (error) {
                const err = formatError(error, getProjectById);
                if (err._normalized?.type === 'partial') {
                    refresh();
                }
                throw err;
            }
            if (data.isAsyncTask) {
                startQueryAsyncTask(true);
                return;
            }
            refresh();
        },
        [refresh, getProjectById, startQueryAsyncTask, selectionOperations.getSelectedInfo, filters, date]
    );
    return [
        {
            pending,
            error,
            data: {summary, rows, totalCount},
            sorter,
            pagination,
            selection,
            asyncTasks,
        },
        {
            setPageNo,
            setPageSize,
            refresh,
            getProjectById,
            getProjectsByKeys,
            onSort,
            selectionOperations,
            inlineSaveProject,
            updateFastProjectFeedName,
            batchUpdateProject,
            modBatchProjectsSeperate,
            batchModProject,
            batchDeleteProject,
        },
    ];
}
