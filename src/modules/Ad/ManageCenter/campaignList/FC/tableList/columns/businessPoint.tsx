
import {Popover} from '@baidu/one-ui';
import {brandBizPoint} from 'commonLibs/businessPoint';
import {IconEdit, IconGradeCircle} from 'dls-icons-react';
import {uniqBy} from 'lodash-es';
import {createCustomRender} from '@/utils/render';
import {CampaignItemOfFC, FcCampaignTypeEnum} from '@/interface/campaign';
import {withSumCell} from '@/utils/handleSummary';
import {FC_MARKET_TARGET} from '@/dicts/marketingTarget';
import {FcAdType} from '@/dicts/fcCampaign';
import {request} from '@/utils/ajax';

const BusinessPoint = ({record, trigger}: {record: CampaignItemOfFC, trigger: any}) => {
    const {businessPointName, businessPointId} = record;
    const tipProps = {
        placement: 'bottomLeft',
        content: (
            <div>
                <p>1.推广业务补充完成后可通过报告查看不同业务的整体数据、大盘数据和竞争数据，方便您分析投放效果，定位流量波动原因。</p>
                <p>2.设置推广业务的方式：计划列表-推广业务列-点击“去补充”。</p>
            </div>
        ),
        trigger: 'hover',
    };

    const onOpenBusinessPointEditor = (campaignId: number) => {
        trigger(
            'openEditor',
            'inline',
            'businessPointEditor',
            campaignId
        );
    };
    if (record.campaignType === FcCampaignTypeEnum.AIX
        || record.marketingTargetId === FC_MARKET_TARGET.B2B) {
        return <>-</>;
    }
    return (
        <div className="column-cell-flex">
            {businessPointId && businessPointId === brandBizPoint ? <IconGradeCircle className="brand-icon" /> : null}
            {businessPointName ? businessPointName : <Popover {...tipProps}>去补充</Popover>}
            <IconEdit
                className="inline-operation-icon"
                onClick={() => onOpenBusinessPointEditor(record.campaignId)}
            />
        </div>
    );
};

export default {
    render: createCustomRender(
        (configs, {trigger}) => withSumCell(
            (text, record) => (
                <BusinessPoint
                    record={record as CampaignItemOfFC}
                    trigger={trigger}
                />
            ),
            {needSum: true}
        )
    ),
    filters: {
        filterType: 'enum',
        async prepareResource(config: any) {
            const templates = await getBusinessPointList();
            return {
                ...config,
                options: [{label: '未设置推广业务', value: 0}].concat(templates),
            };
        },
    },
    config: {
        align: 'left',
        width: 120,
        minWidth: 94,
    },
};
const NoBusinessPoint = 0;
const NoBusinessPointLabel = '未填写推广业务';

async function getBusinessPointList() {
    const res = await request<Array<{
        businessPoints: Array<{businessPointName: string, businessPointId: number}>;
    }>>(
        'thunder/GET/BusinessPointService/getBindBusinessPointList',
        {}
    );
    return uniqBy(res?.[0]?.businessPoints || [], 'businessPointId').map(({
        businessPointId, businessPointName,
    }) => ({
        value: businessPointId,
        label: businessPointId === NoBusinessPoint ? NoBusinessPointLabel : businessPointName,
    }));
}
