import {Tooltip} from '@baidu/one-ui';
import {IconFileAdd} from 'dls-icons-react';
import {sendMonitor} from '@/utils/logger';
import {PLATFORM_ENUM} from '@/dicts';
import {PageType} from '@/dicts/pageType';
import {useAdRoute} from '../../routes';

interface BizLeafProps {
    title: string;
    bizOrProjectId: number;
}

export const BizLeaf = (props: BizLeafProps) => {
    const {title, bizOrProjectId} = props;
    const {linkTo} = useAdRoute();
    const handleAdd = (e: React.MouseEvent) => {
        e.stopPropagation();
        sendMonitor('click', {
            source: 'accountTree',
            level: 'bizLeaf',
            target: 'addCampaign',
            info: PLATFORM_ENUM.FC,
        });
        linkTo(PageType.NewCampaign, {
            query: {
                ...(+bizOrProjectId ? {bizId: bizOrProjectId} : {}),
            },
        });
    };
    return (
        <div className="account-tree-leaf account-tree-biz-leaf">
            <span
                className="account-tree-leaf-text account-tree-leaf-text-biz"
                title={title}
            >
                {title}
            </span>
            <Tooltip placement="bottom" title="新建该推广业务下的方案">
                <span className="account-tree-leaf-operator">
                    <IconFileAdd onClick={handleAdd} />
                </span>
            </Tooltip>
        </div>
    );
};
