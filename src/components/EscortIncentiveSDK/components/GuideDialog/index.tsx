/**
 * @file 通用任务引导弹窗
*/

import {Dialog, DialogDestroyable} from '@baidu/one-ui';
import {LogActionType} from '../../config';
import {TaskBasicInfo} from '../../interface/task';
import './index.global.less';

interface DialogToQinggeProps {
    confirm?: {
        onOk?: () => void;
        onClose?: () => void;
    };
    task: TaskBasicInfo;
    log: (action: LogActionType, params?: any) => void;
}

type Text1 = (task: TaskBasicInfo) => React.ReactNode;
interface GuideDialogConfig {
    getText1: Text1;
    getText2: (() => string) | Text1;
    wrapClassName?: string;
}

/**
 * 通用引导弹窗函数
 */
function createGuideDialog(config: GuideDialogConfig) {
    return ({
        confirm,
        task,
        log,
    }: DialogToQinggeProps) => {
        // 埋点 - 任务引导弹窗曝光
        log(LogActionType.VIEW_GUIDE_DIALOG);

        const onOk = (dialog: DialogDestroyable) => {
            log(LogActionType.GUIDE_DIALOG_CONFIRM);
            confirm?.onOk && confirm.onOk();
            dialog.destroy();
        };


        const dialog = Dialog.confirm({
            title: '',
            needCloseIcon: true,
            destroyOnClose: true,
            content: (
                <div className="banfei-guide-dialog-content">
                    <div className="banfei-guide-dialog-content-bg" />
                    <div className="banfei-guide-dialog-content-text">
                        <div className="text1">
                            {config.getText1(task)}
                        </div>
                        <div className="text2">
                            {config.getText2(task)}
                        </div>
                    </div>
                    <div
                        className="banfei-guide-dialog-content-btn"
                        onClick={() => onOk(dialog)}
                    />
                    <div className="banfei-guide-dialog-content-image-preload"></div>
                </div>
            ),
            footer: [],
            afterClose: () => confirm?.onClose && confirm.onClose(),
            wrapClassName: config.wrapClassName,
        });
    };
}

/**
 * PC任务引导弹窗
 */
export const showGuideDialog = createGuideDialog({
    wrapClassName: 'banfei-guide-dialog',
    getText1: task => {
        const {estimateData = {}, rewardLimit} = task;
        const {conversions, click} = estimateData;
        const estimateLabel = conversions ? `${conversions}个转化` : click ? `${click}个点击` : '';

        return (
            <>
                完成任务预计获得
                <span className="text-number">{estimateLabel}</span>
                <span className="text-union">{estimateLabel ? '和' : ''}</span>
                <span className="text-number">{Math.round(rewardLimit / 10)}元7折优惠券！</span>
            </>
        );
    },
    getText2: () => '任务汇集行业成熟经验，提供账户优化建议',
});

/**
 * 智能翻新引导弹窗
 */
export const showRebuildGuideDialog = createGuideDialog({
    wrapClassName: 'banfei-guide-dialog rebuild-variant',
    getText1: task => {
        const {rewardLimit} = task;
        return (
            <>
                完成任务最高可获得
                <span className="text-number">6000元5折优惠券</span>
            </>
        );
    },
    getText2: () => '使用「智能翻新」功能 提升账户基建水平&预算利用率',
});

/**
 * 老带新
 */
export const showOldBringNewGuideDialog = createGuideDialog({
    wrapClassName: 'banfei-guide-dialog rebuild-variant old-bring-new',
    getText1: () => {
        return (
            <>
                成功推荐福建省/重庆市/武汉市/济南市好友加入百度
            </>
        );
    },
    getText2: () => {
        return (
            <>
                推荐1位好友，可得<span className="text-number">1000元五折神券</span>，快来推荐吧
            </>
        );
    },
});
