{"name": "@baidu/escort-incentive-sdk", "version": "0.0.11", "type": "module", "main": "./dist/index.umd.js", "module": "./dist/index.js", "files": ["dist"], "scripts": {}, "dependencies": {"@baidu/one-ui-pro": "0.2.7", "axios": "^1.4.0", "camelcase": "^8.0.0", "classnames": "^2.3.2", "dls-graphics": "^1.4.0", "dls-icons-react": "^3.28.0", "dls-illustrations-react": "^1.3.3", "fast-json-stable-stringify": "^2.1.0", "less-plugin-dls": "^11.10.0", "lodash-es": "^4.17.21", "query-string": "^8.1.0", "react-intersection-observer": "^9.5.2", "react-suspense-boundary": "^2.3.1", "regenerator-runtime": "^0.13.11", "uuid": "^9.0.0"}, "peerDependencies": {"@baidu/light-ai-react": ">=1.12.1", "@baidu/one-ui": ">=4.36.1", "@baidu/winds-ajax": ">=1.1.3", "@baidu/one-ui-pro": ">=0.2.7", "dls-icons-react": ">=3.19.0", "react": ">=17.0.2", "react-dom": ">=17.0.2"}, "devDependencies": {"@types/lodash-es": "^4.17.8", "@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "chokidar": "^3.5.3", "core-js": "^3.25.2", "dayjs": "^1.11.9", "eslint": "^8.19.0", "glob": "^10.3.3", "huse": "2.0.4", "husky": "^8.0.1", "querystring": "^0.2.1", "rollup-plugin-typescript2": "^0.36.0", "stylelint": "^15.10.1", "typescript": "^4.7.4", "vite": "^4.4.4"}}