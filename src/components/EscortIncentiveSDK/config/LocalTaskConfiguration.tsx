/* eslint-disable max-len */
import {formatTaskEndTime, getDiffDays} from '../utils';
import {TaskIdEnum} from './index';
import {slots as TaskPcSlots} from './Task_PC';
import {slots as TaskRenewalSlots} from './Task_Renewal';
import {TaskOldBringNewSlots} from './Task_OldBringNew';
import {TaskUpperAiRebuildCouponsSlots} from './Task_UpperAiRebuildCoupons';
import {TaskUpperConsumptionCouponsSlots, TaskUpperConsumptionPointsSlots} from './Task_UpperConsumption';

type LocalTaskConfigType = {
    [key in TaskIdEnum]: {
        title: string;
        subTitle: string;
        /**
         * 任务指标单位
         */
        taskIndicatorUnit?: string;
        viewDetailOnClickCard?: boolean;
        getTaskTargetDescription?: (props: any) => string;
        slots: {
            [key: string]: (props: any) => React.JSX.Element | null;
        };
    }
};
export const LocalTaskConfig: LocalTaskConfigType = {
    [TaskIdEnum.ExpansionPC]: {
        title: '优化计算机投放领优惠券',
        subTitle: '提升账户计算机端流量利用，获得更多转化',
        taskIndicatorUnit: '元',
        viewDetailOnClickCard: true,
        getTaskTargetDescription: () => '提升计算机端累计消费',
        slots: TaskPcSlots,
    },
    [TaskIdEnum.ExpansionPC2]: {
        title: '挖掘计算机端流量 领优惠券',
        subTitle: '提升账户计算机端流量利用，获得更多转化',
        taskIndicatorUnit: '元',
        viewDetailOnClickCard: true,
        getTaskTargetDescription: ({userStartTime, taskDuration, daysRemaining}) => {
            const endTime = formatTaskEndTime(daysRemaining, 'YYYY-MM-DD');
            // min{ 任务结束时间 - 任务领取时间, 任务周期 - 1 }
            const diff = Math.min(getDiffDays(userStartTime, endTime), taskDuration - 1);
            return `提升${diff}天计算机端累计消费`;
        },
        slots: TaskPcSlots,
    },
    [TaskIdEnum.ExpansionPC3]: {
        title: '挖掘计算机端流量 领优惠券',
        subTitle: '提升账户计算机端流量利用，获得更多转化',
        taskIndicatorUnit: '元',
        viewDetailOnClickCard: true,
        getTaskTargetDescription: ({userStartTime, taskDuration, daysRemaining}) => {
            const endTime = formatTaskEndTime(daysRemaining, 'YYYY-MM-DD');
            // min{ 任务结束时间 - 任务领取时间, 任务周期 - 1 }
            const diff = Math.min(getDiffDays(userStartTime, endTime), taskDuration - 1);
            return `提升${diff}天计算机端累计消费`;
        },
        slots: TaskPcSlots,
    },
    [TaskIdEnum.Renewal]: {
        title: '限时续费，抢5折优惠券',
        subTitle: '首次续费可获超值优惠',
        slots: TaskRenewalSlots,
    },
    [TaskIdEnum.Renewal2]: {
        title: '限时续费，抢5折优惠券',
        subTitle: '首次续费可获超值优惠',
        slots: TaskRenewalSlots,
    },
    [TaskIdEnum.Wakeup]: {
        title: '限时续费，抢5折优惠券',
        subTitle: '活动期续费可获超值优惠',
        slots: TaskRenewalSlots,
    },
    [TaskIdEnum.UpperConsumptionCoupons]: {
        title: '消费冲刺赢5折优惠券',
        subTitle: '消费越多，奖励越多',
        viewDetailOnClickCard: true,
        slots: TaskUpperConsumptionCouponsSlots,
    },
    [TaskIdEnum.UpperConsumptionPoints]: {
        title: '消费冲刺赢礼品积分',
        subTitle: '消费越多，奖励越多',
        viewDetailOnClickCard: true,
        slots: TaskUpperConsumptionPointsSlots,
    },
    [TaskIdEnum.UpperAiRebuildCoupons]: {
        title: '使用智能翻新奖5折优惠券',
        subTitle: 'AI重构投放，科学调优助力效果提升',
        viewDetailOnClickCard: true,
        slots: TaskUpperAiRebuildCouponsSlots,
    },
    // Q3 联中首续
    [TaskIdEnum.UpperQ3Renewal]: {
        title: '限时续费，抢5折优惠券',
        subTitle: '首次续费可获超值优惠',
        viewDetailOnClickCard: true,
        slots: TaskRenewalSlots,
    },
    // Q3 唤醒激励
    [TaskIdEnum.UpperQ3Wakeup]: {
        title: '限时续费，抢5折优惠券',
        subTitle: '活动期续费可获超值优惠',
        viewDetailOnClickCard: true,
        slots: TaskRenewalSlots,
    },
    // Q3 联中智能翻新激励-任务中心
    [TaskIdEnum.UpperQ3AiRebuildCoupons]: {
        title: '使用智能翻新奖5折优惠券',
        subTitle: 'AI重构投放，科学调优助力效果提升',
        viewDetailOnClickCard: true,
        slots: TaskUpperAiRebuildCouponsSlots,
    },
    // 老带新任务
    [TaskIdEnum.OldBringNew]: {
        title: '推荐好友加入百度营销',
        subTitle: '推荐福建/重庆/武汉/济南的新客户，抢限时优惠',
        viewDetailOnClickCard: true,
        slots: TaskOldBringNewSlots,
    },
};
