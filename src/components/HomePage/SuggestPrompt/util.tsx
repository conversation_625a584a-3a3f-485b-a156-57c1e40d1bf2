/* eslint-disable no-console */
/* eslint-disable complexity */
/**
 * @file SuggestPrompt/util
 * <AUTHOR>
 * @modified <EMAIL>
 */
import {useMedia} from '@huse/media';
import {isEqual, shuffle, concat} from 'lodash-es';
import globalData from '@/utils/globalData';
import {CategoryTag} from '@/components/common/AdviceCard/config';
import {CardKey} from './config';

export function getAccountCampaignNum() {
    const {
        aixFcCampaignNum = 0, aixFeedCampaignNum = 0, fcCampaignNum = 0, feedCampaignNum = 0,
    } = globalData.get('existingMaterialInfo') || {};
    return aixFcCampaignNum + aixFeedCampaignNum + fcCampaignNum + feedCampaignNum;
}

const ToolCardPool = [CardKey.TOOL_KEYWORD_RANK, CardKey.TOOL_AIMAX, CardKey.TOOL_AUTO_RULE];

const CreativeRecommendCardPool = [CardKey.PIC_RECOMMENDS, CardKey.TEXT_RECOMMENDS];

const CardPool = [
    // CardKey.COST_DECLINE,
    CardKey.KEYWORD_RISKY,
    // CardKey.ICG_SUGGEST,
    CardKey.KEYWORD_SUGGEST,
    // CardKey.RISKY,
    // CardKey.SUGGEST,
    ...CreativeRecommendCardPool,
    CardKey.REPORT,
    ...ToolCardPool,
    CardKey.LEARN,
    CardKey.ADD,
];

export enum ECardGroupKey {
    suggest = '优化建议',
    creative = '创意推荐',
    tool = '提效功能',
}

const CardKeyToGroupKey = {
    [CardKey.KEYWORD_RISKY]: ECardGroupKey.suggest,
    [CardKey.ICG_SUGGEST]: ECardGroupKey.suggest,
    [CardKey.KEYWORD_SUGGEST]: ECardGroupKey.suggest,
    [CardKey.PIC_RECOMMENDS]: ECardGroupKey.creative,
    [CardKey.TEXT_RECOMMENDS]: ECardGroupKey.creative,
    [CardKey.TOOL_AIMAX]: ECardGroupKey.tool,
    [CardKey.TOOL_AUTO_RULE]: ECardGroupKey.tool,
    [CardKey.TOOL_KEYWORD_RANK]: ECardGroupKey.tool,
    [CardKey.REPORT]: ECardGroupKey.tool,
    [CardKey.LEARN]: ECardGroupKey.tool,
    [CardKey.ADD]: ECardGroupKey.tool,
} as const;

export const FirstPageCardPool = [
    CardKey.KEYWORD_RISKY,
    CardKey.KEYWORD_SUGGEST,
];

export const useGridColumns = () => {
    const isLargeScreen = useMedia('(min-width: 1920px)');
    return isLargeScreen ? 4 : 3;
};

// 优化建议：
// 1. 如卡片所属优先级分类，则置顶展示并按此优先级排序：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/GdALlo1Lhu/fYp8gE9Waz__qh
// 2. 如卡片不属于该优先级排序的11张卡片，则按照：不可投放>投放受限>潜力机会优先级展示
// 3. maxAixSortPriority的取值，在o端(https://bass.baidu-int.com/module/assist/fc)配置，小问号key:adviceFeConfig
const maxAixSortPriority = 11;
const cardTagSortPriority = [CategoryTag.LaunchStopped, CategoryTag.LimitedLaunch, CategoryTag.PotentialOpportunity];

function sortAixCards(a, b, qgMaxPriority: number = maxAixSortPriority) {
    // 优化建议aixSortPriority<=10的卡片，使用aixSortPriority排序
    if (
        a.params.aixSortPriority <= qgMaxPriority || b.params.aixSortPriority <= qgMaxPriority
    ) {
        return a.params.aixSortPriority - b.params.aixSortPriority;
    }
    // 其余优化建议，按投放停止、投放受限、潜力机会优先级展示
    if (
        cardTagSortPriority.includes(a.params?.attributes?.cardTag?.[0])
        && cardTagSortPriority.includes(b.params?.attributes?.cardTag?.[0])
    ) {
        return cardTagSortPriority.indexOf(a.params?.attributes?.cardTag?.[0])
            - cardTagSortPriority.indexOf(b.params?.attributes?.cardTag?.[0]);
    }
    return CardPool.indexOf(a.key) - CardPool.indexOf(b.key);
}

// eslint-disable-next-line max-len
export function mergeAndSortOptCardsData(data1: Record<CardKey, any[]>, data2: Record<CardKey, any[]>, qgMaxPriority: number) {
    const flatData = data => Object.entries(data).flatMap(([groupKey, items]) => {
        // 给每个项添加原始分组的键
        return items?.map(item => ({
            params: item,
            key: groupKey,
            groupLabel: CardKeyToGroupKey[groupKey as keyof typeof CardKeyToGroupKey],
        }));
    });
    return concat(flatData(data1), flatData(data2)).sort(
        (a, b) => sortAixCards(a, b, qgMaxPriority),
    );
}


// window.isUserRecommendationTest
function getRankingCards(cards: any[], ranking: string[]) {
    const copyCards = [...cards];
    return copyCards.sort((a, b) => {
        const indexA = ranking.indexOf(a.params?.adviceKey || a.key);
        const indexB = ranking.indexOf(b.params?.adviceKey || b.key);
        return indexA - indexB;
    });
}

export class CardRecommender {
    displayedCards: any[] = []; // 维护已展示卡片的状态

    // 重置展示数据
    resetDisplayData() {
        this.displayedCards = []; // 重置为原始数据
    }

    // 首次展示 第一行展示优化建议卡，第二行展示其他卡片
    getFirstDisplayCards(cards: any[], num: number, columnNum: number, otherParams = {}) {
        const {ranking = []} = otherParams as {ranking: string[]};
        console.log('传进来的卡片：', JSON.stringify(cards));
        const rankingCards = getRankingCards(cards, ranking);
        const firstDisplayOrder = CardPool;

        const [suggestCards, otherCards] = cards.reduce((acc, card) => {
            if (card.groupLabel === ECardGroupKey.suggest) {
                acc[0].push(card);
            }
            else {
                // 如果有多张工具卡，首屏仅出一张
                if (ToolCardPool.includes(card.key)) {
                    const toolCard = acc[1].find(item => ToolCardPool.includes(item.key));
                    if (toolCard) {
                        return acc;
                    }
                }
                // 如果有多张创意推荐卡，首屏仅出一张
                if (CreativeRecommendCardPool.includes(card.key)) {
                    const toolCard = acc[1].find(item => CreativeRecommendCardPool.includes(item.key));
                    if (toolCard) {
                        return acc;
                    }
                }
                acc[1].push(card);
            }
            return acc;
        }, [[], []]);

        const result = concat(suggestCards.slice(0, columnNum), otherCards.slice(0, columnNum));

        const remainingCards = cards.filter(card =>
            firstDisplayOrder.includes(card.key)
                && !result.find(displayedCard => isEqual(displayedCard, card))
        );
        // 如果不足num个，从剩余卡片中随机补充
        if (result.length < num && remainingCards.length > 0) {
            const newCards = shuffle(remainingCards).slice(0, num - result.length);
            result.push(...newCards);
        }
        console.log('首次展示卡片: ', result);
        this.displayedCards = result;

        return result;
    }

    // 从剩余的卡片中随机展示num个卡片，可以不足num个
    getNextDisplayCards(cards: any[], num: number) {
        const remainingCards = cards.filter(card =>
            CardPool.includes(card.key)
            && !this.displayedCards.find(displayedCard => isEqual(displayedCard, card))
        );
        let newCards = shuffle(remainingCards).slice(0, num);
        this.displayedCards = [...this.displayedCards, ...newCards];
        if (remainingCards.length === 0) {
            // 重新从所有卡片中抽取num个
            this.resetDisplayData();
            newCards = shuffle(cards).slice(0, num);
            this.displayedCards = [...this.displayedCards, ...newCards];
        }
        return newCards.sort(sortAixCards);
    }

    // 提供外部调用的接口，isFirst参数控制是否首次展示
    getDisplayCards(cards: any[], isFirst = false, num: number, columnNum: number, otherParams = {}) {
        return isFirst ? this.getFirstDisplayCards(cards, num, columnNum, otherParams)
            : this.getNextDisplayCards(cards, num);
    }
}

export const cardRecommender = new CardRecommender();
