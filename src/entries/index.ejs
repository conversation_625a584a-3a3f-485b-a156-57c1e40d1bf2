<!DOCTYPE html>
<html dir="ltr">

<head>
    <meta charset="utf-8">
    <title>轻舸</title>
    <link rel="icon" href="https://cpdfe.cdn.bcebos.com/favicon.ico">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta data-n-head="ssr" name="description" data-hid="description" lang="zh-CN"
        content="百度营销轻舸，说到做到，让营销更省力、更省心。自然语言理解和交流，营销策略无衰减。回归营销本质，全流程操作高效便捷。持续集成AI能力，全面满足多元营销需求。">
    <script>
        (function () {
            if (!window.fetch) {
                return;
            }
            const tryDecode = str => {
                try {
                    return decodeURIComponent(str);
                }
                catch (e) {
                    return str;
                }
            };

            const parse = str => {
                const obj = {};
                const pairs = str.split(/; */);
                for (let i = 0; i < pairs.length; i++) {
                    const pair = pairs[i];
                    let eqIdx = pair.indexOf('=');
                    // skip things that don't look like key=value
                    if (eqIdx >= 0) {
                        const key = pair.substr(0, eqIdx).trim();
                        let val = pair.substr(++eqIdx, pair.length).trim();
                        // quoted values
                        if (val[0] === '"') {
                            val = val.slice(1, -1);
                        }
                        // only assign once
                        if (undefined === obj[key]) {
                            obj[key] = tryDecode(val);
                        }
                    }
                }
                return obj;
            };
            const APP_ID = 756;
            const UC_PRODUCT_APP_ID = {
                FC: 3,
                FEED: 422,
            };
            function getFromCookie(prefixArr = []) {
                const [newPrefix, oldPrefix] = prefixArr;
                return getCookie(`${newPrefix}${APP_ID}`) || getCookie(`${oldPrefix}${APP_ID}`);
            }
            const getCookie = key => {
                const cookies = parse(document.cookie);
                return key ? cookies[key] : cookies;
            };
            const parseCookies = (appid = APP_ID) => {
                const cookies = getCookie();
                if (!cookies) {
                    return null;
                }
                const token = cookies[`CPTK_${appid}`] || cookies[`__cas__st__${appid}`];
                const userId = +cookies[`CPID_${appid}`] || +cookies[`__cas__id__${appid}`];
                return token == null || userId == null ? null : { token, userId };
            };
            let cookies = parseCookies();
            if (cookies == null) {
                return;
            }
            function getQueryString(name) {
                const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
                const result = window.location.search.substr(1).match(reg);
                return result ? decodeURIComponent(result[2]) : null;
            }
            const parseUserId = pathname => {
                return getQueryString('userId');
            };
            const getCookies = (cookies = {}) => {
                const userId = parseUserId();
                if (userId) {
                    cookies.userId = userId;
                }
                return cookies;
            };
            function rand16Num(len) {
                const result = [];
                for (let i = 0; i < len; ++i) {
                    result.push('0123456789abcdef'.charAt(Math.floor(Math.random() * 16)));
                }
                return result.join('');
            }
            function rand8Char(len) {
                const str = '409f4eaaad19464fac33ab8b480asdds';
                const result = [];
                for (let i = 0; i < len; ++i) {
                    result.push(str.charAt(Math.floor(Math.random() * str.length)));
                }
                return result.join('');
            }
            function shuffleString(str) {
                const array = str.split('');
                array.sort(() => 0.5 - Math.random());
                return array.join('');
            }
            function guid() {
                const curr = new Date().valueOf().toString();
                return [rand8Char(8), rand16Num(4), '4' + rand16Num(3), rand16Num(4), shuffleString(curr.substring(0, 12))].join('-');
            }
            const qsStringify = data => {
                const result = Object.keys(data).reduce((res, key) => {
                    res.push([key, encodeURIComponent(data[key])].join('='));
                    return res;
                }, []);
                return result.filter(item => item).join('&');
            }
            window.performance && window.performance.setResourceTimingBufferSize(400);

            const PREFIX = '/hairuo/request.ajax?path=';
            const {token, userId} = getCookies(cookies) || {};
            function prefetchAPI(path, params = JSON.stringify({}), isHairuo = true) {
                const reqid = guid();
                const prefix = isHairuo ? PREFIX : '';
                const p = prefix + path + '&reqid=' + reqid;
                return window.fetch(
                    p,
                    {
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        method: 'POST',
                        body: qsStringify({
                            eventId: guid(),
                            params,
                            path,
                            reqid,
                            token,
                            userid: userId,
                            source: 'aix'
                        })
                    }
                )
                    .then(res => res.json())
                    .then(res => {
                        if (!res || res.status !== 0 || res.redirect === true) {
                            const error = res || new Error();
                            error.message = '提前触发失败';
                            throw error;
                        }
                        return res.data;
                    });
            }
            function parseJsonWithFallback(jsonValue, fallbackValue) {
                try {
                    return JSON.parse(jsonValue);
                } catch {
                    return fallbackValue;
                }
            }
            function subDays(date, days) {
                // 创建一个新的Date对象，确保不会修改原始日期
                let newDate = new Date(date);

                // 将日期转换为毫秒（因为Date对象内部是以毫秒为单位的
                let msInDay = 1000 * 60 * 60 * 24;

                // 从日期中减去指定的天数（以毫秒为单位）
                newDate.setTime(newDate.getTime() - (days * msInDay));

                // 返回新的日期对象
                return newDate;
            }
            function getDateFormatted(date = new Date()) {
                let year = date.getFullYear();
                let month = date.getMonth() + 1; // 注意：月份是从0开始的，所以需要+1
                let day = date.getDate();

                // 如果月份或日期是个位数，前面补0
                month = (month < 10 ? '0' : '') + month;
                day = (day < 10 ? '0' : '') + day;

                return year + '-' + month + '-' + day;
            }

            function compose(middlewares = []) {
                const mdw = middlewares.shift();
                return function (...args) {
                    let result = mdw && mdw(...args);
                    while (middlewares.length) {
                        const mdw = middlewares.shift();
                        result = mdw(result, ...args);
                    }
                    return result;
                };
            }
            window.__prefetchDatasource__ = { $source: {} };
            function prefetchAPIController(name, path, params, isHairuo = true) {
                window.__prefetchDatasource__[name] = prefetchAPI(path, JSON.stringify(params || {}), isHairuo);
                window.__prefetchDatasource__[name].clear = function () {
                    window.__prefetchDatasource__[name] = undefined;
                }
            }
            function $marspro([{ bizHags }]) {
                const withNewCategory = true;
                return { token: '409f4eaa-ad19-464f-ac33-ab8b480asdds', dataAnalysis: true, withNewCategory };
            }
            function $appendTraceId() {
                return { traceId: guid() };
            }
            function getPrefetchSourceName(path) {
                return `__PRELOAD_API_QINGGE___${path}`;
            }
            const isHomePagePath = () => /^\/ad\/overview(\/)?$/.test(location.pathname);
            const bassServiceCommonParams = {
                pageNo: 1,
                pageSize: 20,
                descField: 'createTime',
                desc: true,
                from: 'from=qingge',
            };
            // 数组随机排序, Fisher-Yates洗牌算法，会改变原数组
            function shuffleArray(array) {
                for (let i = array.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [array[i], array[j]] = [array[j], array[i]]; // 交换元素
                }
                return array;
            }
            const riskOptKeys = [
                'modAuditRejectMaterial', 'fixAbnormalTransTypes', 'manageMonitorUrl', 'addMissTransFroms',
                'addCampaign', 'addAdGroup', 'modBudgetForDisplay'
                // 'modCampaignBudget', 'modAccountBudget', 'setOcpcConversionTrack',
            ];
            const suggestOptKeys = [
                'addKeyword', 'fcAdgroupAutoTargeting', 'removeInefficientKeyword',
                'addDeepConversionOptEffect', 'removeConflictNgtWord',
                'removeUncorrelatedKeyword', 'addProjectSharedBudget', 'modLandingPageUrl',
                'optimizeLandingPageQuality', 'addEcpcConversionOptEffect', 'addTransTypes',
                'dynamicTextOptimize', 'dynamicImageOptimize', 'modCampaignEquipmentConversion', 'fcPcOptimize',
                'addCreativeOptimizeCtr', 'addCreativeImageOptimizeCtr', 'modOcpcPriceOptimizeConversion',
                'modBudgetForConversion', 'addUnitProductCategory', 'addUnitProductTravel'
                // modPriceOptimizeClick modPriceOptimizeEcpcConversion
                // 'removeConversionNgtWord',
                // 'removeIneffectiveNgtWord', 'removeDuplicateNgtWord',
            ];
            const toolsOptKeys = ['fcKeywordRanking', 'aiMax']; // 自动规则卡片(autoRules)先下掉了
            const icgOptKeys = ['icgLocEnough'];
            const feedOptKeys = ['modCampaignBudgetFeed', 'modAccountBudgetFeed', 'modFeedBudgetForConversion'];

            // 把优化建议划分成首屏展现和其他卡片，分两次查询
            const splitAdviceKeys = bizAuthBasicInfoResult => {
                const { bizHags = {} } = bizAuthBasicInfoResult || {};
                const isSplitAdviceUser = () => true;

                const { hasAuthAppIds = [] } = bizAuthBasicInfoResult?.aixAccountInitInfo || {};
                const hasFeedAuth = hasAuthAppIds.includes(UC_PRODUCT_APP_ID.FEED);
                const feedAdviceKeys = hasFeedAuth ? feedOptKeys : [];

                const allOptKeys = [...riskOptKeys, ...suggestOptKeys, ...toolsOptKeys, ...feedAdviceKeys, ...icgOptKeys];

                const firstPageAdviceKeys = isSplitAdviceUser() ? [
                    // 高优的、提收关注的卡片
                    'addUnitProductTravel', 'modAuditRejectMaterial', 'modBudgetForDisplay',
                    'addKeyword', 'modCampaignEquipmentConversion', 'fcPcOptimize', 'modBudgetForConversion',
                    'addMissTransFroms',
                    ...icgOptKeys,
                    // feed的优化建议卡
                    ...feedAdviceKeys,
                    // 工具卡片只加载一张
                    toolsOptKeys[0],
                ] : allOptKeys;

                return {
                    firstPageAdviceKeys,
                    remainedAdviceKeys: allOptKeys.filter(i => !firstPageAdviceKeys.includes(i))
                };
            };
            window.isUserRecommendationTest = false;
            const prefetchConfigs = [
                {
                    name: '__PRELOAD_API_QINGGE___aurora/GET/BizAuthService/getBizAuth',
                    path: 'aurora/GET/BizAuthService/getBizAuth',
                    params: {},
                },
                {
                    name: '__PRELOAD_API_QINGGE___aurora/GET/BizAuthService/getBasicInfo',
                    path: 'aurora/GET/BizAuthService/getBasicInfo',
                    params: {},
                },
                {
                    name: '__PRELOAD_API_QINGGE___puppet/GET/MaterialFunction/getAccountInfo',
                    path: 'puppet/GET/MaterialFunction/getAccountInfo',
                    params: {
                        accountFields: [
                            // 状态
                            'userStat',
                            'budget', 'budgetType',
                            'regionTarget', 'regionPriceFactor', 'geoLocationStatus', // 地域
                            'activeTimeout', 'monitorUrl',
                            'budgetOfflineTime',
                            'isSign', // 签署安全协议
                            'accountMonitorUrl', // 账户层级监测链接
                            'offlineTimeData',
                            // 搜索意图地域词【扩展，排除】
                            'excludeQueryRegionStatus', 'queryRegionStatus',
                            // 创意【标题、描述、自动配图】
                            'dynamicCreativeTitleStatus', 'textTitleStatus', 'pictureOptimizeCreativeStatus',
                            'pictureOptimizeSegmentStatus', 'textOptimizeSegmentStatus', 'sysLongLinkSegmentStatus',
                            'longMonitorSublink',
                            'consumeDays', 'balanceSum', 'reOnlineReasons', 'rfqStatus', 'nextDayBudget',
                        ],
                    }
                },
                {
                    name: '__PRELOAD_API_QINGGE___aurora/GET/AixAccountService/getAccountInfo',
                    path: 'aurora/GET/AixAccountService/getAccountInfo',
                    params: {}
                },
                {
                    name: '__PRELOAD_API_QINGGE___puppet/GET/BasicInfoFunction/getBasicInfo',
                    path: 'puppet/GET/BasicInfoFunction/getBasicInfo',
                    params: { userId },
                },
                {
                    name: '__PRELOAD_API_QINGGE___carinae/GET/BasicWebService/basicData',
                    path: 'carinae/GET/BasicWebService/basicData',
                    params: {
                        fields: [
                            'validFlows', 'grayIndustryId', 'mediaPackages', 'places',
                            'newInterestTags', 'appcategories', 'validProductTypes',
                            'tradeId1st', 'tradeId2nd',
                        ]
                    },
                },
                {
                    name: '__PRELOAD_API_QINGGE___carinae/GET/BasicWebService/basicInfo',
                    path: 'carinae/GET/BasicWebService/basicInfo',
                    params: { userId },
                },
                {
                    dependencies: ['__PRELOAD_API_QINGGE___aurora/GET/BizAuthService/getBasicInfo'],
                    name: '__PRELOAD_API_QINGGE___carinae/GET/UserWebService/getUserinfo',
                    path: 'carinae/GET/UserWebService/getUserinfo',
                    params: {},
                    condition: ([bizAuthBasicInfoResult]) => {
                        const { hasAuthAppIds = [] } = bizAuthBasicInfoResult?.aixAccountInitInfo || {};
                        return hasAuthAppIds.includes(UC_PRODUCT_APP_ID.FEED);
                    },
                },
                // 优化建议和提示词方案素材推荐接口，在baseinfo系列接口进入队列后再执行
                {
                    dependencies: ['__PRELOAD_API_QINGGE___aurora/GET/BizAuthService/getBasicInfo'],
                    name: getPrefetchSourceName('optcenter/GET/AdviceService/queryOutline'),
                    path: 'optcenter/GET/AdviceService/queryOutline',
                    middlewares: [([bizAuthBasicInfoResult]) => {
                        return {
                            adviceKeys: splitAdviceKeys(bizAuthBasicInfoResult).firstPageAdviceKeys,
                            source: 249,
                            ...$appendTraceId(),
                        };
                    }],
                },
                // feed优化建议可以用queryOutline接口获取
                {
                    dependencies: ['__PRELOAD_API_QINGGE___aurora/GET/BizAuthService/getBasicInfo'],
                    name: getPrefetchSourceName('optcenter/GET/AdviceService/queryFeedOutline'),
                    path: 'optcenter/GET/AdviceService/queryFeedOutline',
                    params: { "adviceKeys": ['modCampaignBudgetFeed', 'modAccountBudgetFeed', 'modFeedBudgetForConversion'], "source": 249 },
                    // condition: ([bizAuthBasicInfoResult]) => {
                    //     const {hasAuthAppIds = []} = bizAuthBasicInfoResult?.aixAccountInitInfo || {};
                    //     return hasAuthAppIds.includes(UC_PRODUCT_APP_ID.FEED);
                    // },
                    condition: () => false
                },
                {
                    dependencies: ['__PRELOAD_API_QINGGE___aurora/GET/BizAuthService/getBasicInfo'],
                    name: `${getPrefetchSourceName('optcenter/GET/AdviceService/queryOutline')}_remainedKeys`,
                    path: 'optcenter/GET/AdviceService/queryOutline',
                    middlewares: [
                        ([bizAuthBasicInfoResult]) => {
                            return {
                                adviceKeys: splitAdviceKeys(bizAuthBasicInfoResult).remainedAdviceKeys,
                                source: 249,
                                ...$appendTraceId(),
                            };
                        }
                    ],
                    condition: () => false,
                },
                {
                    name: `${getPrefetchSourceName('ash-api/cardRecommendation/getUserRecommendation')}`,
                    path: '/ash-api/cardRecommendation/getUserRecommendation',
                    condition: () => {
                        if (Math.random() < 0.2 || location.href.includes("card_test")){
                            window.isUserRecommendationTest = true;
                            return true;
                        }
                        return false;
                    },
                    isHairuo: false,
                },
                {
                    dependencies: ['__PRELOAD_API_QINGGE___aurora/GET/BizAuthService/getBasicInfo'],
                    name: getPrefetchSourceName('lightning/GET/AixDiagnosisService/validateSuggestOpt'),
                    path: 'lightning/GET/AixDiagnosisService/validateSuggestOpt',
                    params: { "suggestTypes": [1, 2], "campaignIds": [] },
                    condition: ([bizAuthBasicInfoResult]) => {
                        const { aixFcCampaignNum = 0, aixFeedCampaignNum = 0 } = bizAuthBasicInfoResult?.existingMaterialInfo || {};
                        return isHomePagePath() && (+aixFcCampaignNum > 0 || +aixFeedCampaignNum > 0);
                    },
                },
                // 行业动态和营销课程接口
                {
                    dependencies: [
                        '__PRELOAD_API_QINGGE___aurora/GET/BizAuthService/getBasicInfo'
                    ],
                    name: '__PRELOAD_API_QINGGE___bass/GET/ProphetTobService/getProphetTobList',
                    path: 'bass/GET/ProphetTobService/getProphetTobList',
                    params: { ...bassServiceCommonParams, appid: 91 },
                    condition: () => false,
                },
                {
                    dependencies: [
                        '__PRELOAD_API_QINGGE___aurora/GET/BizAuthService/getBasicInfo'
                    ],
                    name: getPrefetchSourceName('bass/GET/CourseToBService/getCourseToBList'),
                    path: 'bass/GET/CourseToBService/getCourseToBList',
                    params: { ...bassServiceCommonParams, productTag: [89], descField: 'showTime' },
                    condition: () => false,
                },
            ];
            prefetchConfigs.forEach(async ({ name, path, params, dependencies, condition, getParams, middlewares, isHairuo }) => {
                let dependenciesResult;
                if (dependencies && dependencies.length) { // 处理dependencies
                    dependenciesResult = await Promise.all(dependencies.map(d => window.__prefetchDatasource__[d]));
                }
                let isPrefetch = condition == null; // 处理condition
                if (typeof condition === 'function') {
                    isPrefetch = !!condition(dependenciesResult);
                }
                const {
                    path: adaptedPath,
                    ...adaptedParams
                } = compose(middlewares)(dependenciesResult, { name, path, params }) || {};
                params = { ...params, ...(getParams ? getParams(adaptedParams) : adaptedParams) };
                path = adaptedPath || path;
                if (isPrefetch) {
                    prefetchAPIController(name, path, params, isHairuo);
                }
                window.__prefetchDatasource__.$source[name] = { $path: path, $params: params };
                if (getParams) {
                    window.__prefetchDatasource__.$source[name].$getParams = getParams;
                }
            });
        }());
    </script>
</head>

<body> </body>

</html>